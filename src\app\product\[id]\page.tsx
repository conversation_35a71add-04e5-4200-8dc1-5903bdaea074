import { Suspense } from 'react';
import ProductDetailSkeleton from '@/components/ProductDetailSkeleton';
import ProductDetailClient from './ProductDetailClient';
import type { Product } from '@/store/useCartStore';

// Types for the product data
interface ProductPageProps {
  params: { id: string };
}

// Server component that fetches product data at BUILD TIME for instant loading
async function fetchProductData(productId: string) {
  try {
    // Import database connection and models directly (Vercel fix pattern)
    const { connectToDatabase } = await import('@/lib/mongodb');
    
    // Dynamic imports with error handling
    let ProductModel, CategoryModel;
    try {
      const productModule = await import('@/models/Product');
      const categoryModule = await import('@/models/Category');
      ProductModel = productModule.Product;
      CategoryModel = categoryModule.Category;
    } catch (importError) {
      console.error('Failed to import models:', importError);
      throw new Error('Model import failed');
    }

    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      console.error('Database connection failed:', dbConnection.error);
      return null;
    }

    // Fetch product directly from database
    try {
      const productData = await ProductModel.findById(productId)
        .populate('category', 'name')
        .select('name description price category imageUrl videoUrl weight shape createdAt')
        .lean();
      
      if (!productData) {
        console.log('Product not found:', productId);
        return null;
      }

      const product: Product = {
        _id: productData._id.toString(),
        name: productData.name,
        price: productData.price,
        images: productData.images || [],
        imageUrl: productData.imageUrl,
        videoUrl: productData.videoUrl,
        weight: productData.weight,
        shape: productData.shape,
        category: productData.category?.toString() || productData.category,
        description: productData.description || { en: '' }
      };

      console.log('Product fetched directly from DB:', product.name);
      return product;
    } catch (error) {
      console.error('Error fetching product:', error);
      return null;
    }
  } catch (error) {
    console.error('Error in fetchProductData:', error);
    // Fallback to API calls if direct DB access fails
    return await fetchProductDataViaAPI(productId);
  }
}

// Fallback function using API calls
async function fetchProductDataViaAPI(productId: string) {
  try {
    // Get the correct base URL for different environments
    const getBaseUrl = () => {
      // In production (Vercel)
      if (process.env.VERCEL_URL) {
        return `https://${process.env.VERCEL_URL}`;
      }
      // Custom production URL
      if (process.env.NEXT_PUBLIC_BASE_URL) {
        return process.env.NEXT_PUBLIC_BASE_URL;
      }
      // Local development
      return 'http://localhost:3000';
    };

    const baseUrl = getBaseUrl();
    console.log('Fallback: Fetching product from API:', baseUrl);
    
    const response = await fetch(`${baseUrl}/api/products/${productId}?fields=detail`, {
      cache: 'force-cache',
      next: { revalidate: 60 }
    });

    if (!response.ok) {
      console.error('API fallback failed:', response.status);
      return null;
    }

    const data = await response.json();
    return data.success ? data.product : null;
  } catch (error) {
    console.error('API fallback also failed:', error);
    return null;
  }
}

// Add revalidation to the page
export const revalidate = 60; // Revalidate every 1 minute

// Generate static params for popular products (optional optimization)
export async function generateStaticParams() {
  try {
    // Import database connection and models directly
    const { connectToDatabase } = await import('@/lib/mongodb');
    const productModule = await import('@/models/Product');
    const ProductModel = productModule.Product;

    // Connect to database
    const dbConnection = await connectToDatabase();
    
    if (dbConnection.status === 'error') {
      console.log('Database connection failed for generateStaticParams');
      return [];
    }

    // Get the most popular/recent products to pre-generate
    const popularProducts = await ProductModel.find({})
      .select('_id')
      .sort({ createdAt: -1 }) // Most recent first
      .limit(50) // Pre-generate top 50 products
      .lean();

    const params = popularProducts.map(product => ({
      id: product._id.toString()
    }));

    console.log(`📦 Pre-generating ${params.length} popular product pages`);
    return params;
  } catch (error) {
    console.error('Error in generateStaticParams:', error);
    return []; // Return empty array if error
  }
}

// Loading placeholder component
function ProductLoading() {
  return <ProductDetailSkeleton />;
}

// Wrapper component for client component with Suspense
function ProductDetailWrapper({ product, productId }: { product: Product; productId: string }) {
  return (
    <Suspense fallback={<ProductLoading />}>
      <ProductDetailClient initialProduct={product} productId={productId} />
    </Suspense>
  );
}

// Main server component - fetches data at BUILD TIME
export default async function ProductPage({ params }: ProductPageProps) {
  // Await params to fix Next.js 15 compatibility
  const resolvedParams = await params;
  const productId = resolvedParams.id;

  // Fetch product data at BUILD TIME - no loading for users! 🚀
  const product = await fetchProductData(productId);

  // Handle product not found
  if (!product) {
    return (
      <div id="product-not-found-container" className="min-h-screen pt-20 px-4 max-w-7xl mx-auto">
        <div id="product-not-found-box" className="bg-red-50 p-6 rounded-lg">
          <h1 className="text-2xl font-semibold text-red-700 mb-2">Product Not Found</h1>
          <p className="text-red-600">The product you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  return (
    <ProductDetailWrapper product={product} productId={productId} />
  );
} 